import { IConnection } from 'edana-microservice';
import { isEmpty, map } from 'lodash';

import {
  CONTENT,
  LEARNING_PLAN,
  LP_CATEGORY,
  LP_MODULE,
  SUBJECT,
} from '../../../../../modules/programs/model/CurriculumStructureViewTypes';
import deserializeDbObject from '../../../../../utils/deserializeDbObject';
import { ACTIVE_ID } from '../../../../../model/Status';
import { PUBLISHED_ID } from '../../../../../modules/programs/model/LearningPlanStatus';

export default async function getCurriculumStructure(
  {
    connection,
  }: {
    connection: IConnection;
  },
  {
    programGroupId,
    tenantId,
    view,
    programStructureLP,
    first,
    count,
    searchQuery,
  }: {
    programGroupId: number;
    view: string;
    programStructureLP: number[];
    tenantId: number;
    first: number;
    count: number;
    searchQuery: string;
  },
) {
  let query = `
  	SELECT pp.ID PROGRAM_ID,
    pp.NAME PROGRAM_NAME,
    ppss.ID SUBJECT_ID,
    ppss.NAME SUBJECT_NAME,
    plp.ID LEARNING_PLAN_ID,
    plp.NAME LEARNING_PLAN_NAME,
    ${
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
        ? 'plpc.ID LEARNING_PLAN_CATEGORY_ID,'
        : 'NULL LEARNING_PLAN_CATEGORY_ID,'
    }
    ${
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
        ? 'plpc.NAME LEARNING_PLAN_CATEGORY_NAME,'
        : 'NULL LEARNING_PLAN_CATEGORY_NAME,'
    }
    ${
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
        ? `(
      SELECT LTRIM(SYS_CONNECT_BY_PATH(plpc1.NAME, ' > '), ' > ')
      FROM PG_LEARNING_PLAN_CATEGORY plpc1
      WHERE plpc1.ID = plpc.ID
      START WITH plpc1.PARENT_ID IS NULL
      CONNECT BY PRIOR plpc1.ID = plpc1.PARENT_ID
    ) AS LEARNING_PLAN_CATEGORY_FULL_PATH,`
        : 'NULL AS LEARNING_PLAN_CATEGORY_FULL_PATH,'
    }
    ${
      view === LP_MODULE.value || view === CONTENT.value
        ? 'plpt.ID MODULE_ID,'
        : 'NULL MODULE_ID,'
    }
    ${
      view === LP_MODULE.value || view === CONTENT.value
        ? 'plpt.NAME MODULE_NAME'
        : 'NULL MODULE_NAME'
    }
		FROM PG_PROGRAM pp  
		JOIN PG_PROGRAM_STRUCT_SUBJECT ppss ON ppss.PROGRAM_ID = pp.ID  
		JOIN PG_LEARNING_PLAN plp ON plp.PROG_STRUCT_SUBJECT_ID = ppss.ID 
		${
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
        ? 'JOIN PG_LEARNING_PLAN_CATEGORY plpc ON plpc.LEARNING_PLAN_ID = plp.ID'
        : ''
    }
		${
      view === LP_MODULE.value || view === CONTENT.value
        ? 'JOIN PG_LEARNING_PLAN_TASK plpt ON plpt.LEARNING_PLAN_CATEGORY_ID = plpc.ID'
        : ''
    }
		WHERE pp.PROGRAM_GROUP_ID = ${programGroupId}
    AND pp.TENANT_ID = ${tenantId} AND pp.STATUS = ${ACTIVE_ID}
    AND ppss.TENANT_ID = ${tenantId} AND ppss.STATUS = ${ACTIVE_ID}
    AND plp.TENANT_ID = ${tenantId} AND plp.STATUS = ${PUBLISHED_ID}
    ${
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
        ? `AND plpc.TENANT_ID = ${tenantId} AND plpc.STATUS = ${PUBLISHED_ID}`
        : ''
    }
    ${
      view === LP_MODULE.value || view === CONTENT.value
        ? `AND plpt.TENANT_ID = ${tenantId} AND plpt.STATUS = ${PUBLISHED_ID}`
        : ''
    }
`;
  if (!isEmpty(programStructureLP)) {
    const joined = programStructureLP.join(',');
    switch (view) {
      case SUBJECT.value:
        query += `AND ppss.PROGRAM_ID IN (${joined})`;
        break;
      case LEARNING_PLAN.value:
        query += `AND plp.PROG_STRUCT_SUBJECT_ID IN (${joined})`;
        break;
      case LP_CATEGORY.value:
        if (
          view === LP_CATEGORY.value ||
          view === LP_MODULE.value ||
          view === CONTENT.value
        ) {
          query += `AND plpc.LEARNING_PLAN_ID IN (${joined})`;
        }
        break;
      case LP_MODULE.value:
        if (view === LP_MODULE.value || view === CONTENT.value) {
          query += `AND plpt.LEARNING_PLAN_CATEGORY_ID IN (${joined})`;
        }
        break;
      case CONTENT.value:
        if (view === LP_MODULE.value || view === CONTENT.value) {
          query += `AND plpt.ID IN (${joined})`;
        }
        break;
      default:
        break;
    }
  }

  if (!isEmpty(searchQuery)) {
    query += `
    AND (
      LOWER(pp.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
      LOWER(ppss.NAME) LIKE LOWER('%' || '${searchQuery}' || '%') OR
      LOWER(plp.NAME) LIKE LOWER('%' || '${searchQuery}' || '%')`;

    if (
      view === LP_CATEGORY.value ||
      view === LP_MODULE.value ||
      view === CONTENT.value
    ) {
      query += ` OR LOWER(plpc.NAME) LIKE LOWER('%' || '${searchQuery}' || '%')`;
    }

    if (view === LP_MODULE.value || view === CONTENT.value) {
      query += ` OR LOWER(plpt.NAME) LIKE LOWER('%' || '${searchQuery}' || '%')`;
    }

    query += `
    )
  `;
  }

  query += `
  ORDER BY pp."SEQUENCE", ppss."SEQUENCE", plp.NAME`;

  if (
    view === LP_CATEGORY.value ||
    view === LP_MODULE.value ||
    view === CONTENT.value
  ) {
    query += `, plpc."SEQUENCE"`;
  }

  if (view === LP_MODULE.value || view === CONTENT.value) {
    query += `, plpt."SEQUENCE"`;
  }

  query += `
  OFFSET ${first} ROWS FETCH NEXT ${count} ROWS ONLY
`;

  const { rows = [] } = await connection.execute(query);
  return map(rows, x => deserializeDbObject<ICurriculumStructureTable>(x));
}

export interface ICurriculumStructureTable {
  programId: number;
  programName: string;
  subjectId: number;
  subjectName: string;
  learningPlanId: number;
  learningPlanName: string;
  learningPlanCategoryId: number;
  learningPlanCategoryName: string;
  learningPlanCategoryFullPath: string;
  moduleId: number;
  moduleName: string;
}
